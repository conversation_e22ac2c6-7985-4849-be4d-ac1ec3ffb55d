@echo off
echo Push Photo Browser to Docker Registry
echo.

REM Prompt for registry details
set /p REGISTRY_URL="Enter your Docker registry URL (e.g., docker.io/yourusername, localhost:5000): "
set /p IMAGE_NAME="Enter image name (default: photo-browser): "
if "%IMAGE_NAME%"=="" set IMAGE_NAME=photo-browser

echo.
echo Setting up multi-architecture builder...
docker buildx create --name multiarch-builder --use --bootstrap 2>nul || echo Builder already exists

echo.
echo Building and pushing multi-architecture image to %REGISTRY_URL%/%IMAGE_NAME%:latest
echo This supports both AMD64 (x86_64) and ARM64 architectures...
echo.

REM Build and push multi-architecture image
docker buildx build --platform linux/amd64,linux/arm64 -t %REGISTRY_URL%/%IMAGE_NAME%:latest --push .

echo.
echo Image pushed successfully!
echo.
echo On your Raspberry Pi, you can now run:
echo docker pull %REGISTRY_URL%/%IMAGE_NAME%:latest
echo docker run -p 80:80 -v /path/to/photos:/app/photos %REGISTRY_URL%/%IMAGE_NAME%:latest
echo.
pause
