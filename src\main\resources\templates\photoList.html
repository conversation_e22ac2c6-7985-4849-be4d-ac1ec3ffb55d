<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Photo Gallery</title>
    <style>
        .gallery {
            display: flex;
            flex-wrap: wrap;
            gap: 10px;
            justify-content: space-evenly;
        }
        .photo {
            width: 150px;
            height: 300px;  /* Fixed total height */
            border: 1px solid #ddd;
            padding: 5px;
            text-align: center;
            position: relative;
            display: flex;
            flex-direction: column;
            background-color: white;
            box-sizing: border-box;
        }
        .photo-image-container {
            width: 100%;
            height: 240px;  /* Fixed height for image container */
            overflow: hidden;
            display: flex;
            align-items: center;
            justify-content: center;
            background-color: #f9f9f9;
            flex-shrink: 0;  /* Prevents container from shrinking */
        }
        .photo img {
            max-width: 100%;
            max-height: 100%;
            object-fit: contain;
            display: block;
        }
        .download-link-container {
            flex: 1;  /* Takes remaining space */
            display: flex;
            align-items: center;
            justify-content: center;
            min-height: 40px;  /* Minimum height for link area */
            margin-top: 8px;
            background-color: #f0f0f0;
            border-radius: 3px;
            position: relative;
            z-index: 10;
        }
        .photo a {
            display: block;
            padding: 8px 5px;
            text-decoration: none;
            color: blue;
            font-size: 14px;
            text-align: center;
            width: 100%;
            box-sizing: border-box;
        }
        /* Optional: Add a hover effect on the download link */
        .photo a:hover {
            color: darkblue;
        }
    </style>
</head>
<body>

<h1>Photo Gallery</h1>

<div class="gallery">
    <th:block th:each="photo : ${photos}">
        <div class="photo">
            <!-- Image Preview Container -->
            <div class="photo-image-container">
                <img th:src="@{'/photos/preview/' + ${photo}}" alt="Image Preview">
            </div>

            <!-- Download Link Container -->
            <div class="download-link-container">
                <a th:href="@{'/photos/download/' + ${photo}}" target="_blank">Download</a>
            </div>
        </div>
    </th:block>
</div>

</body>
</html>
