@echo off
echo Setting up multi-architecture Docker build...
echo.

REM Create a new builder instance that supports multi-platform builds
echo Creating Docker buildx builder...
docker buildx create --name multiarch-builder --use --bootstrap

echo.
echo Building multi-architecture image (AMD64 + ARM64)...
echo This will take a few minutes...

REM Build and push multi-architecture image
docker buildx build --platform linux/amd64,linux/arm64 -t photo-browser:latest --load .

echo.
echo Multi-architecture build complete!
echo.
echo To push to a registry (optional):
echo docker tag photo-browser:latest your-registry/photo-browser:latest
echo docker buildx build --platform linux/amd64,linux/arm64 -t your-registry/photo-browser:latest --push .
echo.
pause
