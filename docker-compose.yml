version: '3.8'

services:
  photo-browser:
    build:
      context: .
      platforms:
        - linux/amd64
        - linux/arm64
    container_name: photo-browser-app
    ports:
      - "80:80"
    volumes:
      # Mount your photos directory - UPDATE THIS PATH to your actual photos folder
      - "C:/Users/<USER>/Downloads/AZ:/app/photos"
      # Optional: Mount additional photo directories if needed
      # - "/path/to/another/photo/folder:/app/photos2"
    environment:
      # Override the static resources location to use the mounted volume
      - SPRING_RESOURCES_STATIC_LOCATIONS=file:/app/photos/
      # Optional: Set other Spring Boot properties
      - SPRING_PROFILES_ACTIVE=docker
    restart: unless-stopped
    # Optional: Add health check
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:80/actuator/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s
