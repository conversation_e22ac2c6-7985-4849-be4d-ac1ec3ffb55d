package com.photo.photoBrowser;

import org.springframework.core.io.Resource;
import org.springframework.core.io.UrlResource;
import org.springframework.http.HttpHeaders;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Controller;
import org.springframework.ui.Model;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;

import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.util.List;
import java.util.stream.Collectors;

@Controller
@RequestMapping("/photos")
public class FileController {

    // Path to your photo directory (on Windows)
    private static final Path PHOTO_DIR = Paths.get("/app/photos/");

     @GetMapping
    public String listPhotos(Model model) throws IOException {
        // Walk through the directory recursively and fetch all files (including subfolders)
        List<String> photoFiles = Files.walk(PHOTO_DIR)
                .filter(Files::isRegularFile)  // Only files (not directories)
                .map(PHOTO_DIR::relativize)    // Make the path relative to PHOTO_DIR
                .map(Path::toString)           // Convert Path to String for easier handling
                .collect(Collectors.toList());

        model.addAttribute("photos", photoFiles);
        return "photoList";  // Returns an HTML template to render the list
    }

    @GetMapping("/preview/{filename:.+}")
    public ResponseEntity<Resource> previewFile(@PathVariable String filename) throws IOException {
        // Resolve the full file path by appending the filename to the base directory
        Path file = PHOTO_DIR.resolve(filename);
        
       // System.out.println("Requested file: " + file);  // Log the file path for debugging

        Resource resource = new UrlResource(file.toUri());
        
        // Dynamically set content type based on file extension (JPG, PNG, etc.)
        String contentType = Files.probeContentType(file);

        return ResponseEntity.ok()
                .header(HttpHeaders.CONTENT_TYPE, contentType != null ? contentType : "application/octet-stream")
                .body(resource);  // Return image as a resource
    }

    @GetMapping("/download/{filename:.+}")
    public ResponseEntity<Resource> downloadFile(@PathVariable String filename) throws IOException {
        // Resolve the full file path by appending the filename to the base directory
        Path file = PHOTO_DIR.resolve(filename);
        Resource resource = new UrlResource(file.toUri());
        return ResponseEntity.ok()
                .header(HttpHeaders.CONTENT_DISPOSITION, "attachment; filename=\"" + filename + "\"")
                .body(resource);  // Return file as a resource for download
    }

}