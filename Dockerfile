# Use multi-architecture OpenJDK 21 as the base image
FROM --platform=$BUILDPLATFORM openjdk:21-jdk-slim

# Set the working directory inside the container
WORKDIR /app

# Copy the Gradle wrapper and build files
COPY gradlew gradlew.bat ./
COPY gradle/ gradle/
COPY build.gradle settings.gradle ./

# Copy the source code
COPY src/ src/

# Make the Gradle wrapper executable
RUN chmod +x gradlew

# Build the application
RUN ./gradlew build -x test

# Create a directory for photos (since the app serves photos from a local directory)
RUN mkdir -p /app/photos

# Expose the port the app runs on
EXPOSE 80

# Run the Spring Boot application
CMD ["java", "-jar", "build/libs/photoBrowser-0.0.1-SNAPSHOT.jar"]
