@echo off
echo Starting Photo Browser Application...
echo.
echo Setting up multi-architecture builder (if not exists)...
docker buildx create --name multiarch-builder --use --bootstrap 2>nul || echo Builder already exists

echo.
echo Building multi-architecture image...
docker buildx build --platform linux/amd64,linux/arm64 -t photo-browser:latest --load .

echo.
echo Starting the Docker container...
docker-compose up -d

echo.
echo Photo Browser is starting up...
echo Once ready, you can access it at: http://localhost:8080
echo.
echo To view logs: docker-compose logs -f
echo To stop: docker-compose down
echo.
pause
